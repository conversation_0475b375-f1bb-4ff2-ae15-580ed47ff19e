import requests
import json
from requests.packages.urllib3.exceptions import InsecureRequestWarning

# Ignore SSL certificate validation
requests.packages.urllib3.disable_warnings(InsecureRequestWarning)

api_url = "https://3.129.253.159/api/RFC/consulta_rfc"
api_key = "65e84be33532fb784c48129675f9eff3a682b27168c0ea744b2cf58ee02337c5"
rfc_to_query = "CVE2609105D1"

data = {
    "apikey": api_key,
    "rfc": rfc_to_query
}

headers = {
    "Content-Type": "application/json",
    "accept": "text/plain"
}

response = requests.post(api_url, data=json.dumps(data), headers=headers, verify=False)

if response.status_code == 200:
    result = response.text
    print(result)
else:
    print("Error:", response.status_code)