import requests
import json
from urllib3.exceptions import InsecureRequestWarning

# Ignore SSL certificate validation
import urllib3
urllib3.disable_warnings(InsecureRequestWarning)

api_url = "https://3.129.253.159/api/RFC/consulta_rfc"
api_key = "65e84be33532fb784c48129675f9eff3a682b27168c0ea744b2cf58ee02337c5"
rfc_to_query = "CVE2609105D1"

data = {
    "apikey": api_key,
    "rfc": rfc_to_query
}

headers = {
    "Content-Type": "application/json",
    "accept": "text/plain"
}

try:
    # Add timeout to prevent hanging indefinitely
    response = requests.post(api_url, data=json.dumps(data), headers=headers, verify=False, timeout=30)

    if response.status_code == 200:
        result = response.text
        print("Respuesta exitosa:")
        print(result)
    else:
        print(f"Error HTTP: {response.status_code}")
        print(f"Respuesta del servidor: {response.text}")

except requests.exceptions.ConnectTimeout:
    print("Error: Tiempo de conexión agotado. El servidor no responde.")
    print("Posibles causas:")
    print("- El servidor está caído")
    print("- Problemas de conectividad de red")
    print("- La URL del API ha cambiado")

except requests.exceptions.ConnectionError as e:
    print(f"Error de conexión: {e}")
    print("No se pudo establecer conexión con el servidor.")

except requests.exceptions.RequestException as e:
    print(f"Error en la petición: {e}")

except Exception as e:
    print(f"Error inesperado: {e}")